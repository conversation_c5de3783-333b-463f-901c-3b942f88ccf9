package csim.maven.plugin.processor;

import com.sun.source.tree.MethodTree;
import com.sun.source.tree.Tree;
import com.sun.source.util.TreeScanner;
import com.sun.source.util.Trees;
import com.sun.tools.javac.tree.JCTree;
import com.sun.tools.javac.tree.TreeMaker;
import com.sun.tools.javac.util.Names;

public class AsyncModelTreeScanner extends TreeScanner<Void, Void> {

    static final String var_name_prefix = "__csim__";


    static final String executor_var_name = var_name_prefix + "executor";
    static final String future_var_name = var_name_prefix + "future";
    static final String exception_var_name = var_name_prefix + "ex";

    private Trees trees;
    private TreeMaker treeMaker;
    private Names names;
    private JCTree.JCClassDecl currentClass;

    public AsyncModelTreeScanner(Trees trees, TreeMaker treeMaker, Names names) {
        this.trees = trees;
        this.treeMaker = treeMaker;
        this.names = names;
    }

    @Override
    public Void visitMethod(MethodTree node, Void aVoid) {

        //根据上下文设置位置
        Void unused = super.visitMethod(node, aVoid);
        String name = node.getName().toString();
        if (name.equals("<init>") || isStaticMethod((JCTree.JCMethodDecl) node) || hasSyncMethodAnnotation((JCTree.JCMethodDecl) node)) {
            return unused;
        }

        // 获取当前方法的父类（类声明）
        JCTree.JCMethodDecl methodDecl = (JCTree.JCMethodDecl) node;
        JCTree.JCClassDecl classDecl = getCurrentClass(methodDecl);

        if (classDecl != null) {
            // 创建内部方法
            JCTree.JCMethodDecl innerMethod = createInnerMethod(methodDecl);
            // 将内部方法添加到类中
            classDecl.defs = classDecl.defs.append(innerMethod);

            // 修改原方法体为调度器
            methodDecl.body = createDispatcherBody(methodDecl);
        }

        return unused;
    }

    @Override
    public Void visitClass(com.sun.source.tree.ClassTree node, Void aVoid) {
        // 保存当前类的引用
        this.currentClass = (JCTree.JCClassDecl) node;
        return super.visitClass(node, aVoid);
    }

    /**
     * 获取当前类声明
     */
    private JCTree.JCClassDecl getCurrentClass(JCTree.JCMethodDecl methodDecl) {
        return this.currentClass;
    }

    /**
     * 创建内部方法（原始方法体）
     */
    private JCTree.JCMethodDecl createInnerMethod(JCTree.JCMethodDecl originalMethod) {
        // 创建新的方法名：原方法名 + "Inner_"
        String innerMethodName = originalMethod.name.toString() + "Inner_";

        // 复制原方法的所有属性，但改变方法名
        JCTree.JCMethodDecl innerMethod = treeMaker.MethodDef(
                originalMethod.mods,  // 修饰符
                names.fromString(innerMethodName),  // 新方法名
                originalMethod.restype,  // 返回类型
                originalMethod.typarams,  // 类型参数
                originalMethod.params,  // 参数列表
                originalMethod.thrown,  // 异常列表
                originalMethod.body,  // 原始方法体
                originalMethod.defaultValue  // 默认值
        );

        return innerMethod;
    }

    /**
     * 创建调度器方法体
     */
    private JCTree.JCBlock createDispatcherBody(JCTree.JCMethodDecl methodDecl) {
        treeMaker.at(methodDecl.pos);

        // 构建条件: executor != null && !executor.inEventLoop()
        JCTree.JCExpression condition = treeMaker.Binary(
                JCTree.Tag.NE,
                treeMaker.Select(treeMaker.Ident(names.fromString("this")), names.fromString("executor")),
                treeMaker.Literal(com.sun.tools.javac.code.TypeTag.BOT, null)
        );

        condition = treeMaker.Binary(
                JCTree.Tag.AND,
                condition,
                treeMaker.Unary(
                        JCTree.Tag.NOT,
                        treeMaker.Apply(
                                com.sun.tools.javac.util.List.nil(),
                                treeMaker.Select(treeMaker.Select(treeMaker.Ident(names.fromString("this")), names.fromString("executor")), names.fromString("inEventLoop")),
                                com.sun.tools.javac.util.List.nil()
                        )
                )
        );

        // 构建内部方法调用的参数
        com.sun.tools.javac.util.List<JCTree.JCExpression> args = com.sun.tools.javac.util.List.nil();
        for (JCTree.JCVariableDecl param : methodDecl.params) {
            args = args.append(treeMaker.Ident(param.name));
        }

        String innerMethodName = methodDecl.name.toString() + "Inner_";

        if (isVoidReturn(methodDecl)) {
            // void 方法的处理
            JCTree.JCStatement asyncCall = createAsyncVoidCall(innerMethodName, args);
            JCTree.JCStatement syncCall = treeMaker.Exec(treeMaker.Apply(
                    com.sun.tools.javac.util.List.nil(),
                    treeMaker.Select(treeMaker.Ident(names.fromString("this")), names.fromString(innerMethodName)),
                    args
            ));

            JCTree.JCIf ifStmt = treeMaker.If(condition, asyncCall, syncCall);
            return treeMaker.Block(0, com.sun.tools.javac.util.List.of(ifStmt));
        } else {
            // 有返回值方法的处理
            JCTree.JCStatement asyncCall = createAsyncNonVoidCall(innerMethodName, args, methodDecl.restype);
            JCTree.JCStatement syncCall = treeMaker.Return(treeMaker.Apply(
                    com.sun.tools.javac.util.List.nil(),
                    treeMaker.Select(treeMaker.Ident(names.fromString("this")), names.fromString(innerMethodName)),
                    args
            ));

            JCTree.JCIf ifStmt = treeMaker.If(condition, asyncCall, syncCall);
            return treeMaker.Block(0, com.sun.tools.javac.util.List.of(ifStmt));
        }
    }

    /**
     * 创建异步 void 方法调用
     */
    private JCTree.JCStatement createAsyncVoidCall(String innerMethodName, com.sun.tools.javac.util.List<JCTree.JCExpression> args) {
        JCTree.JCLambda lambda = treeMaker.Lambda(
                com.sun.tools.javac.util.List.nil(),
                treeMaker.Block(0, com.sun.tools.javac.util.List.of(
                        treeMaker.Exec(
                                treeMaker.Apply(
                                        com.sun.tools.javac.util.List.nil(),
                                        treeMaker.Select(treeMaker.Ident(names.fromString("this")), names.fromString(innerMethodName)),
                                        args
                                )
                        )
                ))
        );

        JCTree.JCExpression executeCall = treeMaker.Apply(
                com.sun.tools.javac.util.List.nil(),
                treeMaker.Select(treeMaker.Select(treeMaker.Ident(names.fromString("this")), names.fromString("executor")), names.fromString("execute")),
                com.sun.tools.javac.util.List.of(lambda)
        );
        return treeMaker.Exec(executeCall);
    }

    /**
     * 创建异步非 void 方法调用
     */
    private JCTree.JCStatement createAsyncNonVoidCall(String innerMethodName, com.sun.tools.javac.util.List<JCTree.JCExpression> args, JCTree.JCExpression returnType) {
        JCTree.JCLambda lambda = treeMaker.Lambda(com.sun.tools.javac.util.List.nil(), treeMaker.Block(0, com.sun.tools.javac.util.List.of(treeMaker.Return(
                treeMaker.Apply(
                        com.sun.tools.javac.util.List.nil(),
                        treeMaker.Select(treeMaker.Ident(names.fromString("this")), names.fromString(innerMethodName)),
                        args
                )
        ))));

        // 构造 executor.submit(() -> this.innerMethod())
        JCTree.JCExpression submitCall = treeMaker.Apply(
                com.sun.tools.javac.util.List.nil(),
                treeMaker.Select(
                        treeMaker.Select(treeMaker.Ident(names.fromString("this")), names.fromString("executor")),
                        names.fromString("submit")
                ),
                com.sun.tools.javac.util.List.of(lambda)
        );

        // 定义 future 变量
        JCTree.JCVariableDecl futureVar = treeMaker.VarDef(
                treeMaker.Modifiers(0),
                names.fromString(future_var_name),
                chainDots("io", "netty","util", "concurrent", "Future"),
                submitCall
        );

        JCTree.JCMethodInvocation futureGet = treeMaker.Apply(
                com.sun.tools.javac.util.List.nil(),
                treeMaker.Select(treeMaker.Ident(names.fromString(future_var_name)), names.fromString("get")),
                com.sun.tools.javac.util.List.nil()
        );

        JCTree.JCTypeCast cast = treeMaker.TypeCast(returnType, futureGet);

        // try 块：直接 return future.get()
        JCTree.JCBlock tryBlock = treeMaker.Block(0, com.sun.tools.javac.util.List.of(treeMaker.Return(cast)));

        // catch 块
        JCTree.JCVariableDecl exceptionParam = treeMaker.VarDef(
                treeMaker.Modifiers(com.sun.tools.javac.code.Flags.PARAMETER),
                names.fromString(exception_var_name),
                treeMaker.Ident(names.fromString("Exception")),
                null
        );

        JCTree.JCThrow throwRuntimeEx = treeMaker.Throw(
                treeMaker.NewClass(
                        null,
                        com.sun.tools.javac.util.List.nil(),
                        treeMaker.Ident(names.fromString("RuntimeException")),
                        com.sun.tools.javac.util.List.of(treeMaker.Ident(names.fromString(exception_var_name))),
                        null
                )
        );

        JCTree.JCCatch catchBlock = treeMaker.Catch(
                exceptionParam,
                treeMaker.Block(0, com.sun.tools.javac.util.List.of(throwRuntimeEx))
        );

        // 最终 try-catch 结构
        JCTree.JCTry tryCatch = treeMaker.Try(tryBlock, com.sun.tools.javac.util.List.of(catchBlock), null);
        com.sun.tools.javac.util.List<JCTree.JCStatement> trueBranchStmts = com.sun.tools.javac.util.List.of(futureVar, tryCatch);

        return treeMaker.Block(0, trueBranchStmts);
    }


    public static boolean isVoidReturn(JCTree.JCMethodDecl methodDecl) {
        Tree.Kind kind = methodDecl.restype.getKind();
        if (kind == Tree.Kind.PRIMITIVE_TYPE) {
            return ((JCTree.JCPrimitiveTypeTree) methodDecl.restype).typetag == com.sun.tools.javac.code.TypeTag.VOID;
        } else {
            return false;
        }
    }

    public static boolean isStaticMethod(JCTree.JCMethodDecl methodDecl) {
        // 获取方法的修饰符
        JCTree.JCModifiers modifiers = methodDecl.mods;

        // 检查修饰符中是否包含 static 标志
        return (modifiers.flags & com.sun.tools.javac.code.Flags.STATIC) != 0;
    }

    public static boolean hasSyncMethodAnnotation(JCTree.JCMethodDecl methodDecl) {
        com.sun.tools.javac.util.List<JCTree.JCAnnotation> annotations = methodDecl.mods.annotations;

        for (JCTree.JCAnnotation annotation : annotations) {
            if (annotation.annotationType.toString().equals("SyncMethod")) {
                return true;
            }
        }
        return false;
    }

    private JCTree.JCExpression chainDots(String... elems) {
        JCTree.JCExpression expr = treeMaker.Ident(names.fromString(elems[0]));
        for (int i = 1; i < elems.length; i++) {
            expr = treeMaker.Select(expr, names.fromString(elems[i]));
        }
        return expr;
    }

    private JCTree.JCExpression chainDots(String elems) {
        String[] split = elems.split("\\.");
        return chainDots(split);
    }

}

