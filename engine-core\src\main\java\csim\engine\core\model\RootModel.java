package csim.engine.core.model;


import csim.engine.core.enums.EngineStatus;
import csim.engine.core.util.ObjectHandlerUtils;
import csim.maven.plugin.annotation.AsyncModel;
import csim.model.api.ModelObject;
import io.netty.channel.DefaultEventLoop;
import io.netty.channel.DefaultEventLoopGroup;
import io.netty.channel.EventLoop;
import io.netty.channel.EventLoopGroup;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.Assert;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

import static csim.engine.core.enums.EngineStatus.*;

/**
 * 调度模型
 */
@Slf4j
@AsyncModel
public class RootModel extends AtomicModel {

    public static final String ROOT_MODEL_ID = "-1";

    private final Map<String, AtomicModel> models = new HashMap<>();

    private EngineStatus status = created;


    private double speed = -1;

    private long physicalTime;

    private long simEndTime;

    public RootModel(long simStartTime, long simEndTime) {
        super( null, new RootModelObject(), "-1", new DefaultModelObjectHandler(), simStartTime);
        models.put(ROOT_MODEL_ID, this);
        this.simEndTime = simEndTime;
    }

    public void addObject(String id, ModelObject object) {
        addObject(ROOT_MODEL_ID, id, object);
    }

    public void addObject(String pid, String id, ModelObject object) {
        AtomicModel parent = models.get(pid);
        Assert.notNull(parent, () -> "父节点[" + pid + "] 不存在");
        AtomicModel child;
        if (ROOT_MODEL_ID.equals(pid)) {
            child = new AtomicModel(parent, object, id, ObjectHandlerUtils.getModelObjectHandler(object), simStartTime);
        } else {
            child = new AtomicModel(parent, object, id, ObjectHandlerUtils.getModelObjectHandler(object), simStartTime);
        }
        AtomicModel old = models.put(id, child);
        Assert.isNull(old, () -> "[" + id + "] 重复");
        parent.addObject(child);
    }

    public void addObject(ModelObject object) {
        addObject(UUID.randomUUID().toString(), object);
    }


    static long ww;

    public void start(long currentTime) {
        log.info("开始推演: 共{}个实体", models.size());
        status = running;
        ww = System.currentTimeMillis();
        tick(currentTime);
    }

    @Override
    public void tick(long logicTime) {
        this.physicalTime = System.currentTimeMillis();
        if (logicTime > simEndTime) {
            status = stopped;
            long cost = System.currentTimeMillis() - ww;
            System.out.println("cost: " + cost + "  倍速：" + (simEndTime - simStartTime)/1.0/cost);
            return;
        }
        super.tick(logicTime);
    }

    @Override
    protected void onTickComplete() {
        if (running.equals(this.status)) {
            long next = next();
            if (speed > 0) {
                long delta = next - getCurrentTime();
                long nextPhysicalTime = this.physicalTime + (long) (delta / speed);
                long nowTime = System.currentTimeMillis();
                if (nowTime < nextPhysicalTime) {
                    long hh = nextPhysicalTime - nowTime;
                    executor.schedule(() -> tick(next), hh, TimeUnit.MILLISECONDS);
                } else {
                    executor.execute(() -> tick(next));
                }
            } else {
                executor.execute(() -> tick(next));
            }

        }
    }
}
